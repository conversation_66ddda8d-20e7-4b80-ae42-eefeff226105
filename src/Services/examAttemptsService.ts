import { createDoc, readCollection, readDoc, updateDocFields } from "@/Services/firestoreService";

export type ExamMode = "practice" | "real" | "bank";

export type DifficultyLevel = "Easy" | "Medium" | "Hard";

export type DifficultyStrategy = "mix" | "fixed";

export interface ExamAttemptSettings {
  totalQuestions: number;
  timeLimitSec?: number; // Only for real exam
  categories?: string[]; // if empty or undefined => all
  difficultyStrategy: DifficultyStrategy;
  difficulty?: DifficultyLevel; // used when strategy is fixed
}

export interface AttemptQuestionResult {
  questionId: string;
  selected?: "A" | "B" | "C" | "D";
  correct?: "A" | "B" | "C" | "D";
  isCorrect?: boolean;
  answeredAt?: number; // epoch ms
  flagged?: boolean;
}

export interface ExamAttemptRecord {
  id: string;
  userId: string;
  certificateId: string;
  mode: ExamMode;
  status: "in-progress" | "completed" | "aborted";
  settings: ExamAttemptSettings;
  startedAt: number; // epoch ms
  completedAt?: number; // epoch ms
  durationSec?: number;
  questions: AttemptQuestionResult[]; // ordered to support navigation
  score?: {
    total: number;
    correct: number;
    percentage: number; // 0..100
  };
}

function attemptsPath(userId: string) {
  return `users/${userId}/examAttempts`;
}

function sanitizeQuestion(q: AttemptQuestionResult): AttemptQuestionResult {
  const out: any = {};
  if (q.questionId) out.questionId = q.questionId;
  if (typeof q.selected !== "undefined") out.selected = q.selected;
  if (typeof q.correct !== "undefined") out.correct = q.correct;
  if (typeof q.isCorrect !== "undefined") out.isCorrect = q.isCorrect;
  if (typeof q.answeredAt !== "undefined") out.answeredAt = q.answeredAt;
  if (typeof q.flagged !== "undefined") out.flagged = q.flagged;
  return out as AttemptQuestionResult;
}

export async function startAttempt(params: {
  userId: string;
  certificateId: string;
  mode: ExamMode;
  settings: ExamAttemptSettings;
  questionIds: string[]; // ordered list to present
}): Promise<ExamAttemptRecord> {
  const attemptId = `attempt_${Date.now()}_${globalThis.crypto?.randomUUID?.() ?? Math.random().toString(36).slice(2)}`;
  const now = Date.now();
  const questions: AttemptQuestionResult[] = params.questionIds.map((qid) => ({ questionId: qid }));

  const sanitizedSettings: ExamAttemptSettings = {
    totalQuestions: params.settings.totalQuestions,
    ...(typeof params.settings.timeLimitSec === "number" ? { timeLimitSec: params.settings.timeLimitSec } : {}),
    ...(Array.isArray(params.settings.categories) && params.settings.categories.length > 0 ? { categories: params.settings.categories } : {}),
    difficultyStrategy: params.settings.difficultyStrategy,
    ...(params.settings.difficulty ? { difficulty: params.settings.difficulty } : {}),
  };

  const record: ExamAttemptRecord = {
    id: attemptId,
    userId: params.userId,
    certificateId: params.certificateId,
    mode: params.mode,
    status: "in-progress",
    settings: sanitizedSettings,
    startedAt: now,
    questions,
  };

  await createDoc(`${attemptsPath(params.userId)}/${attemptId}`, record);
  return record;
}

export async function getAttempt(userId: string, attemptId: string): Promise<ExamAttemptRecord | null> {
  return await readDoc<ExamAttemptRecord>(`${attemptsPath(userId)}/${attemptId}`);
}

export async function listAttempts(userId: string, certificateId?: string): Promise<ExamAttemptRecord[]> {
  const items = await readCollection<ExamAttemptRecord>(attemptsPath(userId));
  return items
    .filter((a) => (certificateId ? a.certificateId === certificateId : true))
    .sort((a, b) => b.startedAt - a.startedAt);
}

export async function recordAnswer(params: {
  userId: string;
  attemptId: string;
  questionId: string;
  selected: "A" | "B" | "C" | "D";
  correct?: "A" | "B" | "C" | "D"; // optional for real exams; correctness can be computed at finish
  isCorrect?: boolean; // if provided, will be stored
}): Promise<void> {
  const attempt = await getAttempt(params.userId, params.attemptId);
  if (!attempt) throw new Error("Attempt not found");
  const updated = { ...attempt } as ExamAttemptRecord;
  const idx = updated.questions.findIndex((q) => q.questionId === params.questionId);
  if (idx >= 0) {
    updated.questions[idx] = {
      ...updated.questions[idx],
      selected: params.selected,
      correct: params.correct ?? updated.questions[idx].correct,
      isCorrect: typeof params.isCorrect === "boolean" ? params.isCorrect : updated.questions[idx].isCorrect,
      answeredAt: Date.now(),
    };
  }
  const sanitized = updated.questions.map(sanitizeQuestion);
  await updateDocFields<ExamAttemptRecord>(`${attemptsPath(params.userId)}/${params.attemptId}`, {
    questions: sanitized,
  });
}

export async function updateQuestionFlag(params: {
  userId: string;
  attemptId: string;
  questionId: string;
  flagged: boolean;
}): Promise<void> {
  const attempt = await getAttempt(params.userId, params.attemptId);
  if (!attempt) throw new Error("Attempt not found");
  const updated = { ...attempt } as ExamAttemptRecord;
  const idx = updated.questions.findIndex((q) => q.questionId === params.questionId);
  if (idx >= 0) {
    updated.questions[idx] = {
      ...updated.questions[idx],
      flagged: params.flagged,
    };
  }
  const sanitized = updated.questions.map(sanitizeQuestion);
  await updateDocFields<ExamAttemptRecord>(`${attemptsPath(params.userId)}/${params.attemptId}`, {
    questions: sanitized,
  });
}

export async function finishAttempt(params: {
  userId: string;
  attemptId: string;
  // If provided, will compute and set score from stored answers
  computeFromAnswers?: boolean;
  // Or caller can provide an explicit score (e.g., for real exam using bank answers at finish)
  score?: { total: number; correct: number; percentage: number };
}): Promise<ExamAttemptRecord> {
  const attempt = await getAttempt(params.userId, params.attemptId);
  if (!attempt) throw new Error("Attempt not found");
  const now = Date.now();
  let score = params.score ?? attempt.score;
  if (!score && params.computeFromAnswers) {
    const answered = attempt.questions.filter((q) => typeof q.isCorrect === "boolean");
    const correct = answered.filter((q) => q.isCorrect).length;
    const total = attempt.questions.length;
    const percentage = total > 0 ? Math.round((correct / total) * 100) : 0;
    score = { total, correct, percentage };
  }

  const updates: Partial<ExamAttemptRecord> = {
    status: "completed",
    completedAt: now,
    durationSec: Math.max(1, Math.floor((now - attempt.startedAt) / 1000)),
    ...(score ? { score } : {}),
  };
  await updateDocFields<ExamAttemptRecord>(`${attemptsPath(params.userId)}/${params.attemptId}`, updates as any);
  const finalDoc = await getAttempt(params.userId, params.attemptId);
  return finalDoc as ExamAttemptRecord;
}

export async function abortAttempt(params: { userId: string; attemptId: string }): Promise<void> {
  await updateDocFields<ExamAttemptRecord>(`${attemptsPath(params.userId)}/${params.attemptId}`, {
    status: "aborted",
    completedAt: Date.now(),
  });
}


