"use client";

type Variant = "practice" | "real" | "bank";

export default function ExamModeCard({
  title,
  description,
  icon,
  onClick,
  variant = "practice",
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  variant?: Variant;
}) {
  return (
    <button
      type="button"
      onClick={onClick}
      className="group flex h-full w-full flex-col items-stretch rounded-2xl border border-border bg-white p-6 text-left shadow-sm transition hover:shadow-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20"
    >
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="inline-flex h-11 w-11 items-center justify-center rounded-xl bg-primary/10 text-primary">
          {icon}
        </div>
        <div className="flex-1">
          <div className="text-lg font-semibold text-charcoal">{title}</div>
          <div className="text-sm text-grey">{description}</div>
        </div>
        <ModeBadge variant={variant} />
      </div>

      {/* Preview of exam UI */}
      <div className="mt-5 flex-1">
        <ModePreview variant={variant} />
      </div>

      {/* Large CTA (takes 50% of card, with DTC emerald animation on hover) */}
      <div className="mt-auto pt-4">
        <div className="relative inline-flex w-1/2 sm:w-1/2">
          {/* Ambient glow on hover */}
          <span className="pointer-events-none absolute inset-0 -z-10 rounded-xl bg-primary/15 opacity-0 blur-md transition-all duration-300 ease-out group-hover:opacity-100 group-hover:scale-105" />
          {/* CTA body (click bubbles to parent button) */}
          <div
            className="relative inline-flex h-12 w-full items-center justify-center rounded-xl border border-primary/40 bg-primary px-4 text-sm font-semibold text-white shadow-sm transition-all duration-300 ease-out group-hover:scale-[1.03] group-hover:bg-primary/10 group-hover:text-primary group-hover:shadow-md group-hover:border-primary"
          >
            <span className="mr-2">Configure & Start</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
          </div>
        </div>
      </div>
    </button>
  );
}

function ModeBadge({ variant }: { variant: Variant }) {
  const map: Record<Variant, { cls: string; text: string }> = {
    practice: { cls: "border-emerald-200 bg-emerald-50 text-emerald-700", text: "Practice" },
    real: { cls: "border-accent/30 bg-accent/10 text-charcoal", text: "Real Exam" },
    bank: { cls: "border-primary/30 bg-primary/10 text-primary", text: "Question Bank" },
  };
  const { cls, text } = map[variant];
  return (
    <span className={`inline-flex items-center rounded-md border px-2 py-1 text-xs ${cls}`}>{text}</span>
  );
}

function ModePreview({ variant }: { variant: Variant }) {
  if (variant === "real") {
    return (
      <div className="mt-5 rounded-xl border border-border bg-muted p-4">
        <div className="mb-3 flex items-center justify-between text-xs text-grey">
          <span>Question 1 of 50</span>
          <span className="inline-flex items-center gap-1 rounded-md border border-border bg-white px-2 py-0.5 font-medium text-charcoal">Timer 60:00</span>
        </div>
        <div className="mb-3 h-3 w-3/4 rounded bg-white/80" />
        <div className="mb-2 h-3 w-2/3 rounded bg-white/70" />
        <div className="space-y-2">
          {[1,2,3,4].map((i) => (
            <div key={i} className="flex items-center gap-3 rounded-lg border border-border bg-white p-2">
              <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-[11px] font-bold text-primary">{String.fromCharCode(64+i)}</span>
              <div className="h-2 w-2/3 rounded bg-muted" />
            </div>
          ))}
        </div>
        <div className="mt-3 inline-flex items-center rounded-md border border-border bg-white px-2 py-1 text-[11px] text-grey">No feedback until finish</div>
      </div>
    );
  }

  if (variant === "bank") {
    return (
      <div className="mt-5 rounded-xl border border-border bg-muted p-4">
        <div className="mb-3 flex items-center justify-between text-xs text-grey">
          <span>Sequential • Q1 → Q2 → Q3 → …</span>
          <span className="inline-flex items-center gap-1 rounded-md border border-border bg-white px-2 py-0.5">No timer</span>
        </div>
        <div className="mb-3 h-3 w-4/5 rounded bg-white/80" />
        <div className="space-y-2">
          {[1,2,3,4].map((i) => (
            <div key={i} className="flex items-center gap-3 rounded-lg border border-border bg-white p-2">
              <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-[11px] font-bold text-primary">{String.fromCharCode(64+i)}</span>
              <div className="h-2 w-1/2 rounded bg-muted" />
            </div>
          ))}
        </div>
        <div className="mt-3 inline-flex items-center rounded-md border border-border bg-white px-2 py-1 text-[11px] text-grey">Immediate feedback on confirm</div>
      </div>
    );
  }

  // practice
  return (
    <div className="mt-5 rounded-xl border border-border bg-muted p-4">
      <div className="mb-3 flex items-center justify-between text-xs text-grey">
        <span>Question 1 of 20</span>
        <span className="inline-flex items-center gap-1 rounded-md border border-emerald-200 bg-emerald-50 px-2 py-0.5 text-emerald-700">Feedback after confirm</span>
      </div>
      <div className="mb-3 h-3 w-3/4 rounded bg-white/80" />
      <div className="space-y-2">
        {[1,2,3,4].map((i) => (
          <div
            key={i}
            className={`flex items-center gap-3 rounded-lg border p-2 ${i===1?"border-emerald-300 bg-emerald-50":"border-border bg-white"} ${i===2?"border-red-300 bg-red-50":""}`}
          >
            <span className={`inline-flex h-5 w-5 items-center justify-center rounded-full text-[11px] font-bold ${i===1?"bg-emerald-100 text-emerald-700":i===2?"bg-red-100 text-red-700":"bg-primary/10 text-primary"}`}>{String.fromCharCode(64+i)}</span>
            <div className="h-2 w-2/3 rounded bg-muted" />
          </div>
        ))}
      </div>
    </div>
  );
}


