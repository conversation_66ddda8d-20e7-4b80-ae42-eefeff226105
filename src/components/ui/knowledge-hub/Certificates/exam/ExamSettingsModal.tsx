"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Minus, Plus } from "lucide-react";
import { DtcModal } from "@/components/ui/DtcModal";
import type { DifficultyLevel } from "@/Services/examAttemptsService";

export type ModeKey = "practice" | "real" | "bank";

export default function ExamSettingsModal({
  open,
  mode,
  totalAvailable,
  loadingPool,
  onClose,
  onStart,
  availableCategories = [],
}: {
  open: boolean;
  mode: ModeKey | null;
  totalAvailable: number;
  loadingPool: boolean;
  onClose: () => void;
  onStart: (settings: { totalQuestions: number; categories?: string[]; difficultyStrategy: "mix" | "fixed"; difficulty?: DifficultyLevel; timeLimitSec?: number }) => void;
  availableCategories?: string[];
}) {
  const [count, setCount] = useState<number>(20);
  const [categories, setCategories] = useState<string[]>([]);
  const [diffStrategy, setDiffStrategy] = useState<"mix" | "fixed">("mix");
  const [difficulty, setDifficulty] = useState<DifficultyLevel>("Medium");
  const [timeMin, setTimeMin] = useState<number>(60);

  const isReal = mode === "real";

  return (
    <DtcModal
      isOpen={open}
      onClose={onClose}
      title={mode === "practice" ? "Practice Exam Settings" : mode === "real" ? "Real Exam Settings" : "Full Bank Settings"}
      description={
        mode === "practice"
          ? "Choose how many questions, categories, and difficulty behavior."
          : mode === "real"
          ? "Select total questions, time, and difficulty behavior. Score is shown at the end."
          : "Answer the full question bank in order, or subset by category."
      }
      size="lg"
      headerClassName="rounded-t-lg bg-[var(--emerald)]"
      titleClassName="text-white"
      descriptionClassName="text-white/80"
    >
      <div className="space-y-6">
        {/* DTC emerald header strip */}
        <div className="rounded-xl bg-gradient-to-r from-primary/15 to-accent/15 p-4 ring-1 ring-primary/10">
          <div className="text-sm font-medium text-charcoal">Configure your exam</div>
          <div className="text-xs text-grey">Available questions: {loadingPool ? "Loading…" : totalAvailable}</div>
        </div>

        {/* Number of questions with animated slider */}
        {mode !== "bank" && (
          <div className="space-y-3">
            <div className="flex items-end justify-between">
              <label className="text-sm font-medium text-charcoal">Number of questions</label>
              <span className="rounded-md border border-primary/30 bg-primary/10 px-2 py-0.5 text-xs font-semibold text-primary">{count}</span>
            </div>
            <Slider
              min={1}
              max={Math.max(1, totalAvailable)}
              value={[count]}
              onValueChange={(v) => setCount(Array.isArray(v) ? Math.max(1, Math.min(v[0], totalAvailable)) : count)}
              className="[&_[data-slot=slider-track]]:h-2 [&_[data-slot=slider-range]]:bg-gradient-to-r [&_[data-slot=slider-range]]:from-primary [&_[data-slot=slider-range]]:to-accent [&_[data-slot=slider-thumb]]:size-5"
            />
          </div>
        )}

        {/* Digital timer control */}
        {isReal && (
          <div className="space-y-4">
            <div className="flex items-end justify-between">
              <label className="text-sm font-medium text-charcoal">Time</label>
              <span className="rounded-md border border-primary/30 bg-primary/10 px-2 py-0.5 text-xs font-semibold text-primary">{timeMin} min</span>
            </div>
            {/* Business timer centered */}
            <div className="flex flex-col items-center gap-4">
              <div className="w-60 max-w-full rounded-xl border border-border bg-white px-6 py-3 shadow-sm">
                <div className="flex items-baseline justify-center gap-2 font-mono tracking-wider">
                  <span className="text-4xl sm:text-5xl font-semibold text-charcoal">
                    {String(Math.floor(timeMin / 60)).padStart(2, "0")}
                  </span>
                  <span className="text-primary text-3xl sm:text-4xl">:</span>
                  <span className="text-4xl sm:text-5xl font-semibold text-charcoal">
                    {String(timeMin % 60).padStart(2, "0")}
                  </span>
                </div>
                <div className="mt-1 text-center text-[11px] text-grey">hours : minutes</div>
              </div>

              {/* Alternate control: stepper inputs */}
              {(() => {
                const hours = Math.floor(timeMin / 60);
                const mins = timeMin % 60;
                const clamp = (v: number) => Math.max(5, Math.min(600, v));
                const setByParts = (h: number, m: number) => {
                  const total = clamp(h * 60 + m);
                  setTimeMin(total);
                };
                return (
                  <div className="flex items-center gap-6">
                    {/* Hours control */}
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-grey">Hours</span>
                      <Button variant="outline" className="h-8 w-8 rounded-md border-border p-0" onClick={() => setByParts(Math.max(0, hours - 1), mins)}>
                        <Minus className="h-4 w-4" />
                      </Button>
                      <input
                        aria-label="Hours"
                        type="number"
                        min={0}
                        max={10}
                        value={hours}
                        onChange={(e) => {
                          const h = Math.max(0, Math.min(10, Number(e.target.value || 0)));
                          setByParts(h, mins);
                        }}
                        className="h-8 w-14 rounded-md border border-border bg-white text-center text-sm"
                      />
                      <Button variant="outline" className="h-8 w-8 rounded-md border-border p-0" onClick={() => setByParts(hours + 1, mins)}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {/* Minutes control */}
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-grey">Minutes</span>
                      <Button variant="outline" className="h-8 w-8 rounded-md border-border p-0" onClick={() => setByParts(hours, Math.max(0, mins - 5))}>
                        <Minus className="h-4 w-4" />
                      </Button>
                      <input
                        aria-label="Minutes"
                        type="number"
                        min={0}
                        max={59}
                        step={5}
                        value={mins}
                        onChange={(e) => {
                          const m = Math.max(0, Math.min(59, Number(e.target.value || 0)));
                          setByParts(hours, m);
                        }}
                        className="h-8 w-16 rounded-md border border-border bg-white text-center text-sm"
                      />
                      <Button variant="outline" className="h-8 w-8 rounded-md border-border p-0" onClick={() => setByParts(hours, Math.min(59, mins + 5))}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        )}
        {/* Categories: return to original chip selector */}
        <div className="flex flex-col gap-2">
          <label className="text-sm text-charcoal">Categories</label>
          {availableCategories.length ? (
            <div className="flex flex-wrap gap-2">
              {availableCategories.map((c) => {
                const active = categories.includes(c);
                return (
                  <button
                    key={c}
                    type="button"
                    onClick={() =>
                      setCategories((prev) =>
                        prev.includes(c) ? prev.filter((x) => x !== c) : [...prev, c]
                      )
                    }
                    className={`rounded-full border px-3 py-1 text-xs ${active ? "border-primary bg-primary/10 text-primary" : "border-border bg-white text-charcoal"}`}
                  >
                    {c}
                  </button>
                );
              })}
              <button
                type="button"
                onClick={() => setCategories([])}
                className="rounded-full border border-border bg-white px-3 py-1 text-xs text-grey"
              >
                Clear
              </button>
            </div>
          ) : (
            <div className="text-xs text-grey">No predefined categories found. All will be used.</div>
          )}
        </div>
        {/* Animated difficulty selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-charcoal">Difficulty</label>
            <div className="flex items-center gap-3 text-sm font-medium text-charcoal">
              <span className={diffStrategy === "mix" ? "text-primary" : "text-grey"}>Mix</span>
              <div className="scale-125">
                <Switch
                  className="h-7 w-12"
                  checked={diffStrategy === "fixed"}
                  onCheckedChange={(v) => setDiffStrategy(v ? "fixed" : "mix")}
                />
              </div>
              <span className={diffStrategy === "fixed" ? "text-primary" : "text-grey"}>Fixed</span>
            </div>
          </div>
          {diffStrategy === "fixed" && (
            <div className="flex gap-2">
              {(["Easy", "Medium", "Hard"] as DifficultyLevel[]).map((lvl) => {
                const active = difficulty === lvl;
                return (
                  <button
                    key={lvl}
                    type="button"
                    onClick={() => setDifficulty(lvl)}
                    className={`flex-1 rounded-lg border px-3 py-2 text-sm transition ${active ? "border-primary bg-primary/10 text-primary" : "border-border bg-white"}`}
                  >
                    {lvl}
                  </button>
                );
              })}
            </div>
          )}
        </div>
        <div className="flex justify-end gap-2 pt-2">
          <Button variant="outline" className="border-border" onClick={onClose}>Cancel</Button>
          <Button
            className="bg-primary text-white"
            onClick={() =>
              onStart({
                totalQuestions: mode === "bank" ? totalAvailable : count,
                categories: categories,
                difficultyStrategy: diffStrategy,
                difficulty: diffStrategy === "fixed" ? difficulty : undefined,
                timeLimitSec: isReal ? timeMin * 60 : undefined,
              })
            }
          >
            Start
          </Button>
        </div>
      </div>
    </DtcModal>
  );
}


