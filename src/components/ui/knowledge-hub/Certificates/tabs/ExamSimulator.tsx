"use client";

import { useState, useMemo, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { Button } from "@/components/ui/button";
import { useQuestions } from "@/hooks/useQuestions";
import type { QuestionRecord } from "@/Services/questionsService";
import type { DifficultyLevel, ExamAttemptRecord, ExamMode } from "@/Services/examAttemptsService";
import { startAttempt, recordAnswer, finishAttempt, updateQuestionFlag } from "@/Services/examAttemptsService";
import { Play, Timer, ListChecks, ChevronLeft, ChevronRight, Flag, Check } from "lucide-react";
import ExamModeCard from "@/components/ui/knowledge-hub/Certificates/exam/ExamModeCard";
import ExamSettingsModal, { type ModeKey as SettingsModeKey } from "@/components/ui/knowledge-hub/Certificates/exam/ExamSettingsModal";
import ExamQuestionCard from "@/components/ui/knowledge-hub/Certificates/exam/ExamQuestionCard";
// InlineScore removed per request
import { getCertificateTaxonomy, type CertificateTaxonomy } from "@/Services/questionsService";
import { toast } from "sonner";

type ModeKey = "practice" | "real" | "bank";

export default function ExamSimulator() {
  const params = useParams();
  const certificateId = String(params?.framework || "");
  const { user, authUser } = useUser();

  const [mode, setMode] = useState<ModeKey | null>(null);
  const [openSettings, setOpenSettings] = useState(false);
  const [attempt, setAttempt] = useState<ExamAttemptRecord | null>(null);
  const [currentIdx, setCurrentIdx] = useState<number>(0);
  const [showResultOnSelect, setShowResultOnSelect] = useState<boolean>(false);
  const [remainingSec, setRemainingSec] = useState<number | null>(null);
  const finishingRef = useRef(false);
  const { data: questions, loading } = useQuestions(certificateId);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  
  const [pending, setPending] = useState<"A" | "B" | "C" | "D" | null>(null);
  const [revealed, setRevealed] = useState<boolean>(false);

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        if (!certificateId) return;
        const tx = await getCertificateTaxonomy(certificateId);
        if (!mounted) return;
        setAvailableCategories(Array.isArray(tx?.groups) ? tx!.groups : []);
      } catch (e) {
        // ignore silently; fall back to no categories
      }
    })();
    return () => {
      mounted = false;
    };
  }, [certificateId]);

  const currentQuestion = useMemo(() => {
    if (!attempt) return null;
    const qid = attempt.questions[currentIdx]?.questionId;
    return questions.find((q) => q.id === qid) ?? null;
  }, [attempt, currentIdx, questions]);

  function openMode(m: ModeKey) {
    setMode(m);
    setOpenSettings(true);
  }

  async function handleStart(settings: {
    totalQuestions: number;
    categories?: string[];
    difficultyStrategy: "mix" | "fixed";
    difficulty?: DifficultyLevel;
    timeLimitSec?: number;
  }) {
    if (!authUser) return;
    const pool = filterQuestionPool(questions, settings.categories, settings.difficultyStrategy, settings.difficulty);
    if (!pool.length) {
      toast.error("No questions match the selected filters");
      return;
    }
    const ordered = buildQuestionOrder(pool, settings.totalQuestions, mode === "bank");
    const record = await startAttempt({
      userId: authUser.uid,
      certificateId,
      mode: (mode as ExamMode) ?? "practice",
      settings,
      questionIds: ordered.map((q) => q.id),
    });
    setAttempt(record);
    setCurrentIdx(0);
    setShowResultOnSelect(mode !== "real");
    setPending(null);
    setRevealed(false);
    setOpenSettings(false);
  }

  async function confirmCurrent() {
    if (!attempt || !currentQuestion || !authUser || !pending) return;
    const isPracticeLike = attempt.mode !== "real"; // practice and bank
    const isCorrect = pending === currentQuestion.correct;
    await recordAnswer({
      userId: authUser.uid,
      attemptId: attempt.id,
      questionId: currentQuestion.id,
      selected: pending,
      correct: isPracticeLike ? currentQuestion.correct : undefined,
      isCorrect: isPracticeLike ? isCorrect : undefined,
    });
    setAttempt((prev) =>
      prev
        ? {
            ...prev,
            questions: prev.questions.map((q, i) =>
              i === currentIdx
                ? { ...q, selected: pending, correct: isPracticeLike ? currentQuestion.correct : q.correct, isCorrect: isPracticeLike ? isCorrect : q.isCorrect, answeredAt: Date.now() }
                : q
            ),
          }
        : prev
    );
    setRevealed(isPracticeLike);
  }

  function skipCurrent() {
    if (!attempt) return;
    setPending(null);
    setRevealed(false);
    setCurrentIdx((i) => Math.min(attempt.questions.length - 1, i + 1));
  }

  async function toggleFlag() {
    if (!attempt || !currentQuestion || !authUser) return;
    const qState = attempt.questions[currentIdx];
    const nextFlag = !qState.flagged;
    await updateQuestionFlag({ userId: authUser.uid, attemptId: attempt.id, questionId: currentQuestion.id, flagged: nextFlag });
    setAttempt((prev) => (prev ? { ...prev, questions: prev.questions.map((q, i) => (i === currentIdx ? { ...q, flagged: nextFlag } : q)) } : prev));
  }

  async function handleFinish() {
    if (!attempt || !authUser) return;
    if (finishingRef.current) return;
    finishingRef.current = true;

    let final: ExamAttemptRecord;
    if (attempt.mode === "real") {
      const score = computeRealScore(attempt, questions);
      final = await finishAttempt({ userId: authUser.uid, attemptId: attempt.id, score });
    } else {
      final = await finishAttempt({ userId: authUser.uid, attemptId: attempt.id, computeFromAnswers: true });
    }
    setAttempt(final);
    finishingRef.current = false;
  }

  function goPrev() {
    setCurrentIdx((i) => Math.max(0, i - 1));
  }
  function goNext() {
    if (!attempt) return;
    setCurrentIdx((i) => Math.min(attempt.questions.length - 1, i + 1));
  }

  // UI
  function computeRealScore(attempt: ExamAttemptRecord, bank: QuestionRecord[]): { total: number; correct: number; percentage: number } {
    const map = new Map(bank.map((q) => [q.id, q] as const));
    let correct = 0;
    let total = attempt.questions.length;
    const updated = attempt.questions.map((q) => {
      const ref = map.get(q.questionId);
      if (!ref) return q;
      const isCorrect = q.selected ? q.selected === ref.correct : false;
      if (isCorrect) correct += 1;
      return { ...q, correct: ref.correct, isCorrect };
    });
    setAttempt((prev) => (prev ? { ...prev, questions: updated } : prev));
    const percentage = total > 0 ? Math.round((correct / total) * 100) : 0;
    return { total, correct, percentage };
  }

  function QuestionIndexBar({ total, current, getState, onJump }: { total: number; current: number; getState: (i: number) => "unanswered"|"answered"|"correct"|"incorrect"|"flagged"; onJump: (i: number) => void }) {
    const indices: number[] = [];
    const start = Math.max(0, current - 5);
    const end = Math.min(total - 1, current + 5);
    for (let i = start; i <= end; i++) indices.push(i);

    return (
      <div className="flex flex-wrap items-center gap-2 rounded-lg border border-gray-100 bg-white p-2 shadow-sm">
        {indices.map((i) => {
          const state = getState(i);
          const classes =
            state === "correct"
              ? "bg-emerald-50 text-emerald-700 border-emerald-200"
              : state === "incorrect"
              ? "bg-red-50 text-red-700 border-red-200"
              : state === "answered"
              ? "bg-primary/10 text-primary border-primary/30"
              : state === "flagged"
              ? "bg-amber-50 text-amber-800 border-amber-200"
              : "bg-white text-charcoal border-border";
          const ring = i === current ? "ring-1 ring-primary" : "";
          return (
            <button
              key={i}
              type="button"
              onClick={() => onJump(i)}
              className={`h-8 w-8 rounded-md border text-xs font-semibold transition ${classes} ${ring}`}
              aria-label={`Go to question ${i + 1}`}
            >
              {i + 1}
            </button>
          );
        })}
        <div className="ml-auto text-xs text-grey">Showing {start + 1}–{end + 1} of {total}</div>
      </div>
    );
  }

  function formatMMSS(totalSec: number): string {
    const mm = Math.floor(totalSec / 60).toString().padStart(2, "0");
    const ss = Math.floor(totalSec % 60).toString().padStart(2, "0");
    return `${mm}:${ss}`;
  }
  // Real exam countdown
  useEffect(() => {
    if (!attempt || attempt.mode !== "real" || !attempt.settings.timeLimitSec) {
      setRemainingSec(null);
      return;
    }
    const endAt = attempt.startedAt + attempt.settings.timeLimitSec * 1000;
    const tick = () => {
      const r = Math.max(0, Math.floor((endAt - Date.now()) / 1000));
      setRemainingSec(r);
      if (r <= 0) {
        // Auto-finish once
        if (attempt.status === "in-progress") {
          handleFinish();
        }
      }
    };
    tick();
    const id = setInterval(tick, 1000);
    return () => clearInterval(id);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attempt?.id, attempt?.mode, attempt?.settings.timeLimitSec]);

  if (!certificateId) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">Select a certificate.</div>
    );
  }

  if (!authUser) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">Please sign in to start exams.</div>
    );
  }

  return (
    <div className="w-full">
      {/* Header removed per request */}

      {!attempt ? (
        <div className="grid gap-6 sm:grid-cols-3 items-stretch">
          <ExamModeCard
            title="Practice Exam"
            icon={<Play className="h-5 w-5" />}
            description="Choose question count and categories. Instant feedback per question."
            onClick={() => openMode("practice")}
            variant="practice"
          />
          <ExamModeCard
            title="Real Exam Mimic"
            icon={<Timer className="h-5 w-5" />}
            description="Timed exam with mixed difficulties. Score shown at the end."
            onClick={() => openMode("real")}
            variant="real"
          />
          <ExamModeCard
            title="Full Question Bank"
            icon={<ListChecks className="h-5 w-5" />}
            description="Answer the entire bank in order. Instant feedback."
            onClick={() => openMode("bank")}
            variant="bank"
          />
        </div>
      ) : (
        <div className="space-y-4">
          {/* Inline header for attempt view only */}
          <div className="flex items-center justify-between">
            <div className="text-sm font-semibold text-charcoal">Mode: {attempt.mode} • Question {currentIdx + 1} / {attempt.questions.length}</div>
            <div className="flex items-center gap-2">
              {attempt.mode === "real" && typeof remainingSec === "number" && (
                <div className={`mr-2 rounded-md border border-border bg-white px-2 py-1 text-xs ${remainingSec <= 30 ? "text-red-600" : "text-charcoal"}`}>
                  {formatMMSS(remainingSec)}
                </div>
              )}
              <Button
                type="button"
                variant={attempt.questions[currentIdx]?.flagged ? "default" : "outline"}
                className={attempt.questions[currentIdx]?.flagged ? "bg-amber-100 text-amber-800 shadow-sm" : "border-border"}
                onClick={toggleFlag}
              >
                <Flag className="mr-2 h-4 w-4" /> {attempt.questions[currentIdx]?.flagged ? "Unflag" : "Flag"}
              </Button>
              <Button variant="outline" className="border-border" onClick={goPrev} disabled={currentIdx === 0}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-border" onClick={goNext} disabled={currentIdx === attempt.questions.length - 1}>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button className="bg-primary text-white" onClick={handleFinish}>Finish</Button>
            </div>
          </div>
          {/* Top index bar */}
          <QuestionIndexBar
            total={attempt.questions.length}
            current={currentIdx}
            getState={(i) => {
              const q = attempt.questions[i];
              if (!q) return "unanswered";
              if (q.flagged) return "flagged" as const;
              if (typeof q.isCorrect === "boolean") return q.isCorrect ? "correct" : "incorrect";
              if (q.selected) return "answered" as const;
              return "unanswered" as const;
            }}
            onJump={(i) => {
              setCurrentIdx(i);
              setPending(null);
              setRevealed(false);
            }}
          />

          {/* Top controls moved into header above for a tighter layout */}
          {attempt.status === "completed" && attempt.score && (
            <div className="rounded-lg border border-gray-100 bg-white p-4 text-sm">
              <span className="font-medium text-charcoal">Final score:</span> {attempt.score.correct}/{attempt.score.total} ({attempt.score.percentage}%)
            </div>
          )}
          {currentQuestion && (
            <div className="space-y-3">
              <ExamQuestionCard
                question={currentQuestion}
                selected={pending ?? attempt.questions[currentIdx]?.selected}
                showImmediate={revealed}
                onSelect={(opt) => setPending(opt)}
                disabled={attempt.mode !== "real" && typeof attempt.questions[currentIdx]?.isCorrect === "boolean"}
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button variant="outline" className="border-border" onClick={skipCurrent}>Skip</Button>
                </div>
                {revealed ? (
                  <Button
                    className="bg-primary text-white shadow-sm hover:shadow-md"
                    onClick={() => {
                      // If last question, finish; else move to next
                      const lastIndex = (attempt?.questions.length ?? 1) - 1;
                      const nextIndex = Math.min(lastIndex, currentIdx + 1);
                      if (currentIdx >= lastIndex) {
                        handleFinish();
                      } else {
                        setCurrentIdx(nextIndex);
                        setPending(null);
                        setRevealed(false);
                      }
                    }}
                  >
                    {currentIdx >= (attempt?.questions.length ?? 1) - 1 ? "Finish" : "Next"} <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button className="bg-primary text-white shadow-sm hover:shadow-md" onClick={confirmCurrent} disabled={!pending}>
                    <Check className="mr-2 h-4 w-4" /> Confirm
                  </Button>
                )}
              </div>
            </div>
          )}
          {/* Progress bar removed per request */}
          
        </div>
      )}

      <ExamSettingsModal
        open={openSettings}
        mode={mode as SettingsModeKey}
        totalAvailable={questions.length}
        loadingPool={loading}
        onClose={() => setOpenSettings(false)}
        onStart={handleStart}
        availableCategories={availableCategories}
      />
    </div>
  );
}
function filterQuestionPool(all: QuestionRecord[], categories?: string[], strategy?: "mix" | "fixed", difficulty?: DifficultyLevel) {
  let pool = all;
  if (categories && categories.length) {
    const set = new Set(categories.map((c) => c.toLowerCase()));
    pool = pool.filter((q) => (q.tags || []).some((t) => set.has(String(t).toLowerCase())) || (q.topic && set.has(q.topic.toLowerCase())));
  }
  if (strategy === "fixed" && difficulty) {
    pool = pool.filter((q) => q.difficulty === difficulty);
  }
  return pool;
}

function buildQuestionOrder(pool: QuestionRecord[], total: number, fullBank: boolean) {
  const copy = [...pool];
  if (fullBank) return copy;
  // random sample without replacement
  const result: QuestionRecord[] = [];
  const max = Math.min(total, copy.length);
  while (result.length < max && copy.length > 0) {
    const idx = Math.floor(Math.random() * copy.length);
    result.push(copy[idx]);
    copy.splice(idx, 1);
  }
  return result;
}
